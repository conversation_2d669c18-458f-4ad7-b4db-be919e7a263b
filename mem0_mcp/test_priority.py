#!/usr/bin/env python3
"""
Test identity priority between context and arguments
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from identity.context_manager import IdentityManager, Identity

def test_identity_priority():
    """Test identity priority between context and arguments"""
    
    print("=== Testing Identity Priority ===")
    
    # Test 1: Context vs Arguments priority
    print("\n1. Testing context vs arguments priority:")
    try:
        # Set context (from URL path)
        context_identity = Identity(user_id="root")
        tokens = IdentityManager.set_identity(context_identity)
        
        # Test with arguments (from request)
        arguments = {"user_id": "previous_user"}
        effective_identity = IdentityManager.get_effective_identity(arguments)
        
        print(f"Context identity: {context_identity}")
        print(f"Arguments: {arguments}")
        print(f"Effective identity: {effective_identity}")
        print(f"Primary ID: {effective_identity.get_primary_id()}")
        print(f"Expected: root (context should take precedence)")
        print(f"Actual: {effective_identity.get_primary_id()}")
        
        IdentityManager.clear_identity(tokens)
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: Arguments only
    print("\n2. Testing arguments only:")
    try:
        arguments = {"user_id": "previous_user"}
        effective_identity = IdentityManager.get_effective_identity(arguments)
        
        print(f"Arguments: {arguments}")
        print(f"Effective identity: {effective_identity}")
        print(f"Primary ID: {effective_identity.get_primary_id()}")
        print(f"Expected: previous_user")
        print(f"Actual: {effective_identity.get_primary_id()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Context only
    print("\n3. Testing context only:")
    try:
        context_identity = Identity(user_id="root")
        tokens = IdentityManager.set_identity(context_identity)
        
        effective_identity = IdentityManager.get_effective_identity({})
        
        print(f"Context identity: {context_identity}")
        print(f"Effective identity: {effective_identity}")
        print(f"Primary ID: {effective_identity.get_primary_id()}")
        print(f"Expected: root")
        print(f"Actual: {effective_identity.get_primary_id()}")
        
        IdentityManager.clear_identity(tokens)
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_identity_priority() 