#!/usr/bin/env python3
"""
Test the actual request structure to understand the issue
"""

import json

def test_request_structure():
    """Test the actual request structure"""
    
    print("=== Testing Request Structure ===")
    
    # Simulate the actual request from logs
    request_params = {
        'name': 'add_memory', 
        'arguments': {
            'messages': [{'role': 'user', 'content': 'test message'}],
            'user_id': 'user123',
            'agent_id': 'agent456', 
            'run_id': 'run789'
        },
        '_meta': {'progressToken': 1}
    }
    
    print(f"Original request params: {json.dumps(request_params, indent=2)}")
    
    # Simulate the MCP handler logic
    tool_name = request_params.get("name")
    arguments = request_params.get("arguments", {})
    
    print(f"\nExtracted tool_name: {tool_name}")
    print(f"Extracted arguments: {json.dumps(arguments, indent=2)}")
    
    # Test the identity extraction logic
    identity_params = ["user_id", "agent_id", "run_id", "session_id"]
    extracted_identity = {}
    
    print(f"\nTesting identity extraction:")
    for param in identity_params:
        in_request = param in request_params
        in_arguments = param in arguments
        condition = in_request and not in_arguments
        print(f"  {param}: in_request={in_request}, in_arguments={in_arguments}, condition={condition}")
        
        if condition:
            arguments[param] = request_params[param]
            extracted_identity[param] = request_params[param]
    
    print(f"\nExtracted identity: {extracted_identity}")
    print(f"Final arguments: {json.dumps(arguments, indent=2)}")
    
    print(f"\nProblem analysis:")
    print(f"  - user_id is in request_params (inside arguments object)")
    print(f"  - user_id is also in arguments object")
    print(f"  - So condition 'param in request_params and param not in arguments' is FALSE")
    print(f"  - Therefore, identity parameters are NOT extracted from top-level")
    print(f"  - The arguments object keeps its original identity values")

if __name__ == "__main__":
    test_request_structure() 