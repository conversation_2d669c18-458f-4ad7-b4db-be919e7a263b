# MCP Server Environment Configuration Template
# Copy this file to .env and customize for your environment

# External Mem0 API Configuration (Required)
# Point to your existing Mem0 API server
MEM0_BASE_URL=http://localhost:8000
MEM0_API_VERSION=v1
MEM0_API_KEY=

# MCP Server Configuration
MCP_LOG_LEVEL=INFO
MCP_MAX_CONCURRENT_REQUESTS=100
MCP_REQUEST_TIMEOUT=30

# Identity Management
MCP_DEFAULT_USER_ID=default
MCP_ENABLE_CONTEXT_VARIABLES=true

# Example configurations for different scenarios:

# Scenario 1: Local Mem0 server (same machine)
# MEM0_BASE_URL=http://localhost:8000

# Scenario 2: Remote Mem0 server
# MEM0_BASE_URL=https://your-mem0-api.example.com
# MEM0_API_KEY=your-api-key-here

# Scenario 3: Docker network (if running Mem0 in same Docker network)
# MEM0_BASE_URL=http://mem0-server:8000