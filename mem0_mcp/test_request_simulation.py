#!/usr/bin/env python3
"""
Simulate request processing to test identity priority
"""

import asyncio
import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from identity.context_manager import IdentityManager, Identity
from tools.base_tool import BaseTool
from utils.logger import setup_logging

# Setup logging
setup_logging(level="DEBUG")

class SimulatedTool(BaseTool):
    """Simulated tool to test identity resolution"""
    
    def __init__(self):
        super().__init__("simulated_tool", "Simulated tool for testing")
    
    async def execute(self, arguments):
        """Execute simulated tool"""
        try:
            # Get identity
            identity = self.get_user_identity(arguments)
            print(f"TOOL: Resolved identity: {identity}")
            print(f"TOOL: Primary ID: {identity.get_primary_id()}")
            print(f"TOOL: Arguments received: {arguments}")
            
            return {
                "identity": identity.__dict__,
                "primary_id": identity.get_primary_id(),
                "arguments": arguments
            }
        except Exception as e:
            print(f"TOOL: Error: {e}")
            return {"error": str(e)}
    
    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "user_id": {"type": "string"},
                "agent_id": {"type": "string"},
                "run_id": {"type": "string"},
                "messages": {"type": "array"}
            }
        }

async def simulate_request_processing():
    """Simulate the request processing flow"""
    
    print("=== Simulating Request Processing ===")
    
    # Simulate URL path identity (from /mcp/root)
    url_identity = Identity(user_id="root")
    print(f"\n1. Setting URL identity: {url_identity}")
    
    # Set context from URL
    tokens = IdentityManager.set_identity(url_identity)
    
    # Simulate request parameters (what frontend might send)
    request_params = {
        "name": "add_memory",
        "arguments": {
            "messages": [{"role": "user", "content": "test message"}],
            "user_id": "previous_user"  # This might be from previous session
        }
    }
    
    print(f"\n2. Request parameters: {json.dumps(request_params, indent=2)}")
    
    # Simulate the identity extraction logic from mcp_handler.py
    arguments = request_params.get("arguments", {})
    identity_params = ["user_id", "agent_id", "run_id", "session_id"]
    extracted_identity = {}
    
    for param in identity_params:
        if param in request_params and param not in arguments:
            arguments[param] = request_params[param]
            extracted_identity[param] = request_params[param]
    
    print(f"\n3. Extracted identity from params: {extracted_identity}")
    print(f"4. Final arguments: {json.dumps(arguments, indent=2)}")
    
    # Test tool execution
    tool = SimulatedTool()
    result = await tool.execute(arguments)
    
    print(f"\n5. Tool execution result: {json.dumps(result, indent=2)}")
    
    # Clean up
    IdentityManager.clear_identity(tokens)
    
    print(f"\n6. Expected behavior:")
    print(f"   - URL identity (root) should take precedence")
    print(f"   - But arguments user_id (previous_user) might override it")
    print(f"   - This depends on the get_effective_identity logic")

if __name__ == "__main__":
    asyncio.run(simulate_request_processing()) 