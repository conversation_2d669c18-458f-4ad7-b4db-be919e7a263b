#!/usr/bin/env python3
"""
Test contextvars behavior in async environment
"""

import asyncio
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from identity.context_manager import Identity<PERSON>ana<PERSON>, Identity

async def test_async_context():
    """Test contextvars in async environment"""
    
    print("=== Testing Async Context ===")
    
    # Test 1: Set context in one async function
    print("\n1. Testing context in async function:")
    try:
        async def set_context():
            identity = Identity(user_id="async_root")
            tokens = IdentityManager.set_identity(identity)
            print(f"Set context in async function: {identity}")
            return tokens
        
        async def get_context():
            identity = IdentityManager.get_current_identity()
            print(f"Get context in async function: {identity}")
            return identity
        
        # Set context
        tokens = await set_context()
        
        # Get context in same async context
        identity = await get_context()
        print(f"Context retrieved: {identity}")
        print(f"Primary ID: {identity.get_primary_id()}")
        
        # Clean up
        IdentityManager.clear_identity(tokens)
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: Context across different async tasks
    print("\n2. Testing context across different async tasks:")
    try:
        async def task_with_context():
            identity = Identity(user_id="task_user")
            tokens = IdentityManager.set_identity(identity)
            print(f"Task set context: {identity}")
            
            # Create a new task
            await asyncio.create_task(get_context_in_task())
            
            IdentityManager.clear_identity(tokens)
        
        async def get_context_in_task():
            identity = IdentityManager.get_current_identity()
            print(f"Task get context: {identity}")
            print(f"Primary ID: {identity.get_primary_id()}")
        
        await task_with_context()
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Context in nested async calls
    print("\n3. Testing context in nested async calls:")
    try:
        async def outer_function():
            identity = Identity(user_id="outer_user")
            tokens = IdentityManager.set_identity(identity)
            print(f"Outer set context: {identity}")
            
            await inner_function()
            
            IdentityManager.clear_identity(tokens)
        
        async def inner_function():
            identity = IdentityManager.get_current_identity()
            print(f"Inner get context: {identity}")
            print(f"Primary ID: {identity.get_primary_id()}")
        
        await outer_function()
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_async_context()) 