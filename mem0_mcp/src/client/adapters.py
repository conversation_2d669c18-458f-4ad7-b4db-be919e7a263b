"""
API version adapters for different Mem0 API versions
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional

from .mem0_client import Mem0HTTPClient
from ..utils.errors import ToolExecutionError
from ..utils.logger import get_logger

logger = get_logger(__name__)


class BaseAdapter(ABC):
    """Base adapter for Mem0 API versions"""
    
    def __init__(self, client: Mem0HTTPClient):
        self.client = client
    
    @abstractmethod
    async def add_memory(self, messages: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """Add memory from messages"""
        pass
    
    @abstractmethod
    async def search_memories(self, query: str, **kwargs) -> Dict[str, Any]:
        """Search memories"""
        pass
    
    @abstractmethod
    async def get_memories(self, **kwargs) -> Dict[str, Any]:
        """Get memories"""
        pass
    
    @abstractmethod
    async def get_memory_by_id(self, memory_id: str) -> Dict[str, Any]:
        """Get memory by ID"""
        pass
    
    @abstractmethod
    async def delete_memory(self, memory_id: str) -> Dict[str, Any]:
        """Delete memory by ID"""
        pass
    
    @abstractmethod
    async def update_memory(self, memory_id: str, data: str) -> Dict[str, Any]:
        """Update memory by ID"""
        pass
    
    @abstractmethod
    async def batch_delete_memories(self, **kwargs) -> Dict[str, Any]:
        """Batch delete memories"""
        pass
    
    # Graph database operations
    @abstractmethod
    async def create_graph_entity(self, **kwargs) -> Dict[str, Any]:
        """Create graph entity"""
        pass
    
    @abstractmethod
    async def get_graph_entities(self, **kwargs) -> Dict[str, Any]:
        """Get graph entities"""
        pass
    
    @abstractmethod
    async def update_graph_entity(self, entity_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph entity"""
        pass
    
    @abstractmethod
    async def delete_graph_entity(self, entity_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph entity"""
        pass
    
    @abstractmethod
    async def create_graph_relationship(self, **kwargs) -> Dict[str, Any]:
        """Create graph relationship"""
        pass
    
    @abstractmethod
    async def get_graph_relationships(self, **kwargs) -> Dict[str, Any]:
        """Get graph relationships"""
        pass
    
    @abstractmethod
    async def update_graph_relationship(self, relationship_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph relationship"""
        pass
    
    @abstractmethod
    async def delete_graph_relationship(self, relationship_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph relationship"""
        pass


class V1Adapter(BaseAdapter):
    """Adapter for Mem0 V1 API"""
    
    async def add_memory(self, messages: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Add memory using V1 API
        
        Args:
            messages: List of message objects with role and content
            **kwargs: Additional parameters (user_id, agent_id, run_id, metadata)
            
        Returns:
            V1 API response
        """
        try:
            payload = {
                "messages": messages,
                **kwargs
            }
            
            response = await self.client.post("memories", payload)
            logger.debug(f"V1 add_memory response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 add_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to add memory: {str(e)}", "add_memory")
    
    async def search_memories(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search memories using V2 API (for better search capabilities)
        
        Args:
            query: Search query
            **kwargs: Additional parameters (user_id, agent_id, run_id, filters, limit)
            
        Returns:
            V2 API response
        """
        try:
            # Build V2 search request with proper filters structure
            filters = kwargs.get("filters", {})
            
            # Extract identity parameters and add to filters
            for param in ["user_id", "agent_id", "run_id"]:
                if param in kwargs and kwargs[param] is not None:
                    filters[param] = kwargs[param]
            
            payload = {
                "query": query,
                "version": "v2",  # Force v2 for advanced search capabilities
                "filters": filters,
            }
            
            # Add other optional parameters
            for param in ["limit", "keyword_search", "rerank", "filter_memories"]:
                if param in kwargs and kwargs[param] is not None:
                    payload[param] = kwargs[param]
            
            # Use v2-specific search endpoint
            response = await self.client.post("v2/memories/search/", payload)
            logger.debug(f"V1->V2 search_memories response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1->V2 search_memories failed: {str(e)}")
            raise ToolExecutionError(f"Failed to search memories: {str(e)}", "search_memories")
    
    async def get_memories(self, **kwargs) -> Dict[str, Any]:
        """
        Get memories using V2 API (for better filtering capabilities)
        
        Args:
            **kwargs: Parameters (user_id, agent_id, run_id, filters, etc.)
            
        Returns:
            V2 API response
        """
        try:
            # Build V2 get request with proper filters structure
            filters = kwargs.get("filters", {})
            
            # Extract identity parameters and add to filters
            for param in ["user_id", "agent_id", "run_id"]:
                if param in kwargs and kwargs[param] is not None:
                    filters[param] = kwargs[param]
            
            payload = {
                "version": "v2",  # Force v2 for advanced filtering capabilities
                "filters": filters,
            }
            
            # Add other optional parameters
            if "limit" in kwargs and kwargs["limit"] is not None:
                payload["limit"] = kwargs["limit"]
            
            # Use v2-specific get endpoint
            response = await self.client.post("v2/memories/", payload)
            logger.debug(f"V1->V2 get_memories response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1->V2 get_memories failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get memories: {str(e)}", "get_memories")
    
    async def get_memory_by_id(self, memory_id: str) -> Dict[str, Any]:
        """
        Get memory by ID using V1 API
        
        Args:
            memory_id: Memory ID
            
        Returns:
            V1 API response
        """
        try:
            response = await self.client.get(f"memories/{memory_id}/")
            logger.debug(f"V1 get_memory_by_id response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 get_memory_by_id failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get memory: {str(e)}", "get_memory_by_id")
    
    async def delete_memory(self, memory_id: str) -> Dict[str, Any]:
        """
        Delete memory by ID using V1 API
        
        Args:
            memory_id: Memory ID
            
        Returns:
            V1 API response
        """
        try:
            response = await self.client.delete(f"memories/{memory_id}/")
            logger.debug(f"V1 delete_memory response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 delete_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to delete memory: {str(e)}", "delete_memory")
    
    async def update_memory(self, memory_id: str, data: str) -> Dict[str, Any]:
        """
        Update memory by ID using V1 API

        Args:
            memory_id: Memory ID
            data: New memory content

        Returns:
            V1 API response
        """
        try:
            payload = {
                "memory": data
            }
            
            response = await self.client.put(f"memories/{memory_id}/", payload)
            logger.debug(f"V1 update_memory response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 update_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to update memory: {str(e)}", "update_memory")
    
    async def batch_delete_memories(self, **kwargs) -> Dict[str, Any]:
        """
        Batch delete memories using V1 API
        
        Args:
            **kwargs: Parameters (memory_ids: List[str] - required array of memory IDs)
            
        Returns:
            V1 API response
        """
        try:
            # 根据OpenAPI规范，batch delete需要memory_ids数组
            memory_ids = kwargs.get('memory_ids', [])
            
            if not memory_ids:
                raise ToolExecutionError(
                    "memory_ids parameter is required and must be a non-empty array of memory IDs", 
                    "batch_delete_memories"
                )
            
            payload = {
                "memory_ids": memory_ids  # 正确的参数名和格式
            }
            
            # 使用_make_request方法直接发送DELETE请求（因为需要请求体）
            response = await self.client._make_request("DELETE", "batch/", data=payload)
            logger.debug(f"V1 batch_delete_memories response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 batch_delete_memories failed: {str(e)}")
            raise ToolExecutionError(f"Failed to batch delete memories: {str(e)}", "batch_delete_memories")
    
    # Graph database operations for V1 API
    async def create_graph_entity(self, **kwargs) -> Dict[str, Any]:
        """Create graph entity using V1 API"""
        try:
            payload = {k: v for k, v in kwargs.items() if k not in ['user_id', 'agent_id', 'run_id']}
            params = {k: v for k, v in kwargs.items() if k in ['user_id', 'agent_id', 'run_id']}
            
            response = await self.client.post("graph/nodes/", payload, params=params)
            logger.debug(f"V1 create_graph_entity response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 create_graph_entity failed: {str(e)}")
            raise ToolExecutionError(f"Failed to create graph entity: {str(e)}", "create_graph_entity")
    
    async def get_graph_entities(self, **kwargs) -> Dict[str, Any]:
        """Get graph entities using V1 API"""
        try:
            response = await self.client.get("graph/nodes/", params=kwargs)
            logger.debug(f"V1 get_graph_entities response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 get_graph_entities failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get graph entities: {str(e)}", "get_graph_entities")
    
    async def update_graph_entity(self, entity_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph entity using V1 API"""
        try:
            payload = {**data, **kwargs}
            
            response = await self.client.put(f"graph/nodes/{entity_id}/", payload)
            logger.debug(f"V1 update_graph_entity response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 update_graph_entity failed: {str(e)}")
            raise ToolExecutionError(f"Failed to update graph entity: {str(e)}", "update_graph_entity")
    
    async def delete_graph_entity(self, entity_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph entity using V1 API"""
        try:
            response = await self.client.delete(f"graph/nodes/{entity_id}/", params=kwargs)
            logger.debug(f"V1 delete_graph_entity response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 delete_graph_entity failed: {str(e)}")
            raise ToolExecutionError(f"Failed to delete graph entity: {str(e)}", "delete_graph_entity")
    
    async def create_graph_relationship(self, **kwargs) -> Dict[str, Any]:
        """Create graph relationship using V1 API"""
        try:
            payload = {k: v for k, v in kwargs.items() if k not in ['user_id', 'agent_id', 'run_id']}
            params = {k: v for k, v in kwargs.items() if k in ['user_id', 'agent_id', 'run_id']}
            
            response = await self.client.post("graph/relationships/", payload, params=params)
            logger.debug(f"V1 create_graph_relationship response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 create_graph_relationship failed: {str(e)}")
            raise ToolExecutionError(f"Failed to create graph relationship: {str(e)}", "create_graph_relationship")
    
    async def get_graph_relationships(self, **kwargs) -> Dict[str, Any]:
        """Get graph relationships using V1 API"""
        try:
            response = await self.client.get("graph/relationships/", params=kwargs)
            logger.debug(f"V1 get_graph_relationships response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 get_graph_relationships failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get graph relationships: {str(e)}", "get_graph_relationships")
    
    async def update_graph_relationship(self, relationship_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph relationship using V1 API"""
        try:
            payload = {**data, **kwargs}
            
            response = await self.client.put(f"graph/relationships/{relationship_id}/", payload)
            logger.debug(f"V1 update_graph_relationship response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 update_graph_relationship failed: {str(e)}")
            raise ToolExecutionError(f"Failed to update graph relationship: {str(e)}", "update_graph_relationship")
    
    async def delete_graph_relationship(self, relationship_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph relationship using V1 API"""
        try:
            response = await self.client.delete(f"graph/relationships/{relationship_id}/", params=kwargs)
            logger.debug(f"V1 delete_graph_relationship response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V1 delete_graph_relationship failed: {str(e)}")
            raise ToolExecutionError(f"Failed to delete graph relationship: {str(e)}", "delete_graph_relationship")
    

class HybridAdapter(BaseAdapter):
    """
    Hybrid adapter that intelligently selects v1/v2 endpoints based on feature requirements
    
    - Graph memory: v1 endpoints for relations support
    - Contextual add: v2 endpoints for automatic history retrieval  
    - Regular operations: v2 endpoints for advanced features
    """
    
    def __init__(self, client: 'Mem0HTTPClient'):
        self.client = client
        logger.info("Initialized HybridAdapter with intelligent endpoint selection")
    
    async def add_memory(self, messages: List[Dict[str, Any]], **kwargs) -> Dict[str, Any]:
        """
        Add memory with intelligent endpoint selection
        
        Rules:
        - enable_graph=True → v1 endpoint for graph relations
        - version="v2" explicit → v2 endpoint for contextual add
        - default → v2 endpoint for best features
        """
        try:
            if kwargs.get("enable_graph"):
                # Graph memory: use v1 endpoint
                payload = {
                    "messages": messages,
                    "version": "v1",  # Required for graph relations
                    **kwargs
                }
                response = await self.client.post("memories", payload)
                logger.debug(f"Graph memory add (v1): {response}")
                return response
            else:
                # Regular/contextual add: use v2 endpoint
                payload = {
                    "messages": messages,
                    "version": kwargs.get("version", "v2"),  # Default v2 for contextual add
                    **kwargs
                }
                response = await self.client.post("memories", payload)
                logger.debug(f"Regular add (v2): {response}")
                return response
                
        except Exception as e:
            logger.error(f"HybridAdapter add_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to add memory: {str(e)}", "add_memory")
    
    async def search_memories(self, query: str, **kwargs) -> Dict[str, Any]:
        """
        Search memories with intelligent endpoint selection
        
        Rules:
        - enable_graph=True → v1 search endpoint for graph relations
        - regular search → v2 search endpoint for advanced filtering
        """
        try:
            if kwargs.get("enable_graph"):
                # Graph memory search: use v1 endpoint with direct parameters
                payload = {
                    "query": query,
                    **kwargs  # Direct parameters for v1
                }
                response = await self.client.post("memories/search", payload)
                logger.debug(f"Graph memory search (v1): {response}")
                return response
            else:
                # Regular search: use v2 endpoint with filters structure
                filters = kwargs.get("filters", {})
                
                # Move identity params to filters
                for param in ["user_id", "agent_id", "run_id"]:
                    if param in kwargs and kwargs[param] is not None:
                        filters[param] = kwargs[param]
                
                payload = {
                    "query": query,
                    "version": "v2",
                    "filters": filters,
                }
                
                # Add v2 advanced parameters
                for param in ["limit", "keyword_search", "rerank", "filter_memories"]:
                    if param in kwargs and kwargs[param] is not None:
                        payload[param] = kwargs[param]
                
                response = await self.client.post("v2/memories/search/", payload)
                logger.debug(f"Regular search (v2): {response}")
                return response
                
        except Exception as e:
            logger.error(f"HybridAdapter search_memories failed: {str(e)}")
            raise ToolExecutionError(f"Failed to search memories: {str(e)}", "search_memories")
    
    async def get_memories(self, **kwargs) -> Dict[str, Any]:
        """
        Get memories using V2 API
        
        Args:
            **kwargs: Parameters (filters, pagination, sort, etc.)
            
        Returns:
            V2 API response
        """
        try:
            # Build V2 get request with proper filters structure
            filters = kwargs.get("filters", {})
            
            # Extract identity parameters and add to filters
            for param in ["user_id", "agent_id", "run_id"]:
                if param in kwargs and kwargs[param] is not None:
                    filters[param] = kwargs[param]
            
            payload = {
                "version": "v2",  # Ensure v2 version is specified
                "filters": filters,
            }
            
            # Add other optional parameters
            if "limit" in kwargs and kwargs["limit"] is not None:
                payload["limit"] = kwargs["limit"]
            
            response = await self.client.post("v2/memories/", payload)
            logger.debug(f"V2 get_memories response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 get_memories failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get memories: {str(e)}", "get_memories")
    
    async def get_memory_by_id(self, memory_id: str) -> Dict[str, Any]:
        """
        Get memory by ID using V2 API
        
        Args:
            memory_id: Memory ID
            
        Returns:
            V2 API response
        """
        try:
            response = await self.client.get(f"memories/{memory_id}/")
            logger.debug(f"V2 get_memory_by_id response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 get_memory_by_id failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get memory: {str(e)}", "get_memory_by_id")
    
    async def delete_memory(self, memory_id: str) -> Dict[str, Any]:
        """
        Delete memory by ID using V2 API
        
        Args:
            memory_id: Memory ID
            
        Returns:
            V2 API response
        """
        try:
            response = await self.client.delete(f"memories/{memory_id}/")
            logger.debug(f"V2 delete_memory response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 delete_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to delete memory: {str(e)}", "delete_memory")
    
    async def update_memory(self, memory_id: str, data: str) -> Dict[str, Any]:
        """
        Update memory by ID using V2 API

        Args:
            memory_id: Memory ID
            data: New memory content

        Returns:
            V2 API response
        """
        try:
            payload = {
                "memory": data
            }
            
            response = await self.client.put(f"memories/{memory_id}/", payload)
            logger.debug(f"V2 update_memory response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 update_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to update memory: {str(e)}", "update_memory")
    
    async def batch_delete_memories(self, **kwargs) -> Dict[str, Any]:
        """
        Batch delete memories using V2 API
        
        Args:
            **kwargs: Parameters (memory_ids: List[str] - required array of memory IDs)
            
        Returns:
            V2 API response
        """
        try:
            # 根据OpenAPI规范，batch delete需要memory_ids数组
            memory_ids = kwargs.get('memory_ids', [])
            
            if not memory_ids:
                raise ToolExecutionError(
                    "memory_ids parameter is required and must be a non-empty array of memory IDs", 
                    "batch_delete_memories"
                )
            
            payload = {
                "memory_ids": memory_ids  # 正确的参数名和格式
            }
            
            # 使用_make_request方法直接发送DELETE请求
            response = await self.client._make_request("DELETE", "batch/", data=payload)
            logger.debug(f"V2 batch_delete_memories response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 batch_delete_memories failed: {str(e)}")
            raise ToolExecutionError(f"Failed to batch delete memories: {str(e)}", "batch_delete_memories")
    
    # Graph database operations for V2 API
    async def create_graph_entity(self, **kwargs) -> Dict[str, Any]:
        """Create graph entity using V2 API"""
        try:
            payload = {k: v for k, v in kwargs.items() if k not in ['user_id', 'agent_id', 'run_id']}
            params = {k: v for k, v in kwargs.items() if k in ['user_id', 'agent_id', 'run_id']}
            
            response = await self.client.post("graph/nodes/", payload, params=params)
            logger.debug(f"V2 create_graph_entity response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 create_graph_entity failed: {str(e)}")
            raise ToolExecutionError(f"Failed to create graph entity: {str(e)}", "create_graph_entity")
    
    async def get_graph_entities(self, **kwargs) -> Dict[str, Any]:
        """Get graph entities using V2 API"""
        try:
            # V2 might use POST with JSON body for complex filtering
            response = await self.client.post("graph/nodes/query/", kwargs)
            logger.debug(f"V2 get_graph_entities response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 get_graph_entities failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get graph entities: {str(e)}", "get_graph_entities")
    
    async def update_graph_entity(self, entity_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph entity using V2 API"""
        try:
            payload = {**data, **kwargs}
            
            response = await self.client.put(f"graph/nodes/{entity_id}/", payload)
            logger.debug(f"V2 update_graph_entity response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 update_graph_entity failed: {str(e)}")
            raise ToolExecutionError(f"Failed to update graph entity: {str(e)}", "update_graph_entity")
    
    async def delete_graph_entity(self, entity_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph entity using V2 API"""
        try:
            response = await self.client.delete(f"graph/nodes/{entity_id}/", params=kwargs)
            logger.debug(f"V2 delete_graph_entity response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 delete_graph_entity failed: {str(e)}")
            raise ToolExecutionError(f"Failed to delete graph entity: {str(e)}", "delete_graph_entity")
    
    async def create_graph_relationship(self, **kwargs) -> Dict[str, Any]:
        """Create graph relationship using V2 API"""
        try:
            payload = {k: v for k, v in kwargs.items() if k not in ['user_id', 'agent_id', 'run_id']}
            params = {k: v for k, v in kwargs.items() if k in ['user_id', 'agent_id', 'run_id']}
            
            response = await self.client.post("graph/relationships/", payload, params=params)
            logger.debug(f"V2 create_graph_relationship response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 create_graph_relationship failed: {str(e)}")
            raise ToolExecutionError(f"Failed to create graph relationship: {str(e)}", "create_graph_relationship")
    
    async def get_graph_relationships(self, **kwargs) -> Dict[str, Any]:
        """Get graph relationships using V2 API"""
        try:
            # V2 might use POST with JSON body for complex filtering
            response = await self.client.post("graph/relationships/query/", kwargs)
            logger.debug(f"V2 get_graph_relationships response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 get_graph_relationships failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get graph relationships: {str(e)}", "get_graph_relationships")
    
    async def update_graph_relationship(self, relationship_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph relationship using V2 API"""
        try:
            payload = {**data, **kwargs}
            
            response = await self.client.put(f"graph/relationships/{relationship_id}/", payload)
            logger.debug(f"V2 update_graph_relationship response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 update_graph_relationship failed: {str(e)}")
            raise ToolExecutionError(f"Failed to update graph relationship: {str(e)}", "update_graph_relationship")
    
    async def delete_graph_relationship(self, relationship_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph relationship using V2 API"""
        try:
            response = await self.client.delete(f"graph/relationships/{relationship_id}/", params=kwargs)
            logger.debug(f"V2 delete_graph_relationship response: {response}")
            return response
            
        except Exception as e:
            logger.error(f"V2 delete_graph_relationship failed: {str(e)}")
            raise ToolExecutionError(f"Failed to delete graph relationship: {str(e)}", "delete_graph_relationship")