#!/usr/bin/env python3
"""
Debug script to test identity resolution
"""

import asyncio
import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from identity.context_manager import IdentityManager, Identity
from tools.base_tool import BaseTool
from utils.logger import setup_logging

# Setup logging
setup_logging(level="DEBUG")

class DebugTool(BaseTool):
    """Debug tool to test identity resolution"""
    
    def __init__(self):
        super().__init__("debug_tool", "Debug tool for testing identity")
    
    async def execute(self, arguments):
        """Execute debug tool"""
        try:
            # Get identity
            identity = self.get_user_identity(arguments)
            print(f"DEBUG: Resolved identity: {identity}")
            print(f"DEBUG: Primary ID: {identity.get_primary_id()}")
            print(f"DEBUG: Is valid: {identity.is_valid()}")
            
            return {
                "identity": identity.__dict__,
                "primary_id": identity.get_primary_id(),
                "is_valid": identity.is_valid()
            }
        except Exception as e:
            print(f"DEBUG: Error: {e}")
            return {"error": str(e)}
    
    def get_input_schema(self):
        return {
            "type": "object",
            "properties": {
                "user_id": {"type": "string"},
                "agent_id": {"type": "string"},
                "run_id": {"type": "string"}
            }
        }

async def test_identity_resolution():
    """Test identity resolution with different scenarios"""
    
    tool = DebugTool()
    
    print("=== Testing Identity Resolution ===")
    
    # Test 1: No arguments, no context
    print("\n1. Testing with no arguments and no context:")
    try:
        result = await tool.execute({})
        print(f"Result: {result}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: With user_id in arguments
    print("\n2. Testing with user_id in arguments:")
    try:
        result = await tool.execute({"user_id": "test_user"})
        print(f"Result: {result}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Set context and test
    print("\n3. Testing with context set:")
    try:
        identity = Identity(user_id="context_user")
        tokens = IdentityManager.set_identity(identity)
        
        result = await tool.execute({})
        print(f"Result: {result}")
        
        IdentityManager.clear_identity(tokens)
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: Context vs arguments priority
    print("\n4. Testing context vs arguments priority:")
    try:
        identity = Identity(user_id="context_user")
        tokens = IdentityManager.set_identity(identity)
        
        result = await tool.execute({"user_id": "args_user"})
        print(f"Result: {result}")
        
        IdentityManager.clear_identity(tokens)
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    asyncio.run(test_identity_resolution()) 