#!/usr/bin/env python3
"""
Debug script to isolate the slice error in MCP search
"""
import asyncio
import sys
import os
import traceback

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.config.settings import MCPConfig
from src.client.adapters import V1Adapter, V2Adapter
from src.client.mem0_client import Mem0HTTPClient

async def test_adapters():
    """Test both V1 and V2 adapters directly"""
    config = MCPConfig()
    config.mem0_base_url = "http://localhost:8000"
    config.mem0_api_version = "v2"
    config.mem0_api_key = "dummy-key"
    
    client = Mem0HTTPClient(config)
    
    # Test V2 adapter
    print("Testing V2 adapter...")
    v2_adapter = V2Adapter(client)
    
    try:
        response = await v2_adapter.search_memories(
            "MCP测试",
            user_id="mcp_v2_test_user",
            limit=3
        )
        print(f"V2 Response type: {type(response)}")
        print(f"V2 Response: {response}")
    except Exception as e:
        print(f"V2 Error: {str(e)}")
        traceback.print_exc()
    
    await client.close()

if __name__ == "__main__":
    asyncio.run(test_adapters())