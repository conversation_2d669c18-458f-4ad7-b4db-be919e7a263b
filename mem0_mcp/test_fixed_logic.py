#!/usr/bin/env python3
"""
Test the fixed identity extraction logic
"""

import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from identity.context_manager import Identity<PERSON>anager, Identity

def test_fixed_logic():
    """Test the fixed identity extraction logic"""
    
    print("=== Testing Fixed Identity Logic ===")
    
    # Simulate the actual request from logs
    request_params = {
        'name': 'add_memory', 
        'arguments': {
            'messages': [{'role': 'user', 'content': 'test message'}],
            'user_id': 'user123',
            'agent_id': 'agent456', 
            'run_id': 'run789'
        },
        '_meta': {'progressToken': 1}
    }
    
    print(f"Original request params: {json.dumps(request_params, indent=2)}")
    
    # Simulate the MCP handler logic
    tool_name = request_params.get("name")
    arguments = request_params.get("arguments", {})
    
    print(f"\nExtracted tool_name: {tool_name}")
    print(f"Extracted arguments: {json.dumps(arguments, indent=2)}")
    
    # Test the original identity extraction logic
    identity_params = ["user_id", "agent_id", "run_id", "session_id"]
    extracted_identity = {}
    
    print(f"\n1. Original identity extraction:")
    for param in identity_params:
        in_request = param in request_params
        in_arguments = param in arguments
        condition = in_request and not in_arguments
        print(f"  {param}: in_request={in_request}, in_arguments={in_arguments}, condition={condition}")
        
        if condition:
            arguments[param] = request_params[param]
            extracted_identity[param] = request_params[param]
    
    print(f"Extracted identity: {extracted_identity}")
    print(f"Arguments after extraction: {json.dumps(arguments, indent=2)}")
    
    # Test the new context override logic
    print(f"\n2. Context override logic:")
    
    # Set context identity (from URL path)
    context_identity = Identity(user_id="root")
    tokens = IdentityManager.set_identity(context_identity)
    
    try:
        current_identity = IdentityManager.get_current_identity()
        print(f"Context identity: {current_identity}")
        
        if current_identity.is_valid():
            print(f"Context identity is valid")
            # Override arguments with context identity if context is valid
            if current_identity.user_id and current_identity.user_id != arguments.get('user_id'):
                print(f"Overriding user_id from '{arguments.get('user_id')}' to '{current_identity.user_id}'")
                arguments['user_id'] = current_identity.user_id
            if current_identity.agent_id and current_identity.agent_id != arguments.get('agent_id'):
                print(f"Overriding agent_id from '{arguments.get('agent_id')}' to '{current_identity.agent_id}'")
                arguments['agent_id'] = current_identity.agent_id
            if current_identity.run_id and current_identity.run_id != arguments.get('run_id'):
                print(f"Overriding run_id from '{arguments.get('run_id')}' to '{current_identity.run_id}'")
                arguments['run_id'] = current_identity.run_id
        else:
            print(f"Context identity is not valid")
    except Exception as e:
        print(f"Error getting context identity: {e}")
    
    print(f"\nFinal arguments after context override: {json.dumps(arguments, indent=2)}")
    
    # Clean up
    IdentityManager.clear_identity(tokens)
    
    print(f"\n3. Expected behavior:")
    print(f"  - URL identity (root) should override arguments identity (user123)")
    print(f"  - Final user_id should be 'root'")
    print(f"  - Final arguments: {arguments}")

if __name__ == "__main__":
    test_fixed_logic() 