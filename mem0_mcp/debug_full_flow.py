#!/usr/bin/env python3
"""
Debug the full request flow to identify the issue
"""

import asyncio
import json
import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from identity.context_manager import IdentityManager, Identity

async def debug_full_flow():
    """Debug the full request flow"""
    
    print("=== Debugging Full Request Flow ===")
    
    # Simulate the exact flow from HTTP transport to tool execution
    
    # Step 1: URL path parsing (/mcp/root)
    print("\n1. URL Path Parsing:")
    url_path = "/mcp/root"
    path_parts = url_path.split('/')
    print(f"URL path: {url_path}")
    print(f"Path parts: {path_parts}")
    
    # Extract identity_value from URL
    if len(path_parts) >= 3 and path_parts[1] == "mcp":
        identity_value = path_parts[2]
        print(f"Extracted identity_value: {identity_value}")
    else:
        print("No identity_value found in URL")
        return
    
    # Step 2: Set identity from URL path
    print("\n2. Setting Identity from URL:")
    url_identity = Identity(user_id=identity_value)
    print(f"URL identity: {url_identity}")
    
    # Set context
    tokens = IdentityManager.set_identity(url_identity)
    print(f"Context set with tokens: {tokens}")
    
    # Step 3: Simulate request parameters (what frontend sends)
    print("\n3. Request Parameters:")
    request_params = {
        "name": "add_memory",
        "arguments": {
            "messages": [{"role": "user", "content": "test message"}],
            "user_id": "previous_user"  # This might be from previous session
        }
    }
    print(f"Request params: {json.dumps(request_params, indent=2)}")
    
    # Step 4: Simulate MCP handler identity extraction
    print("\n4. MCP Handler Identity Extraction:")
    arguments = request_params.get("arguments", {})
    identity_params = ["user_id", "agent_id", "run_id", "session_id"]
    extracted_identity = {}
    
    for param in identity_params:
        if param in request_params and param not in arguments:
            arguments[param] = request_params[param]
            extracted_identity[param] = request_params[param]
    
    print(f"Extracted identity from params: {extracted_identity}")
    print(f"Final arguments: {json.dumps(arguments, indent=2)}")
    
    # Step 5: Tool execution with identity resolution
    print("\n5. Tool Identity Resolution:")
    try:
        # Simulate tool.get_user_identity(arguments)
        effective_identity = IdentityManager.get_effective_identity(arguments)
        print(f"Effective identity: {effective_identity}")
        print(f"Primary ID: {effective_identity.get_primary_id()}")
        print(f"Is valid: {effective_identity.is_valid()}")
        
        # Check what should be used for API call
        api_params = {}
        if effective_identity.user_id:
            api_params["user_id"] = effective_identity.user_id
        if effective_identity.agent_id:
            api_params["agent_id"] = effective_identity.agent_id
        if effective_identity.run_id:
            api_params["run_id"] = effective_identity.run_id
        
        print(f"API params: {api_params}")
        
    except Exception as e:
        print(f"Error in identity resolution: {e}")
    
    # Step 6: Clean up
    print("\n6. Cleanup:")
    IdentityManager.clear_identity(tokens)
    print("Context cleared")
    
    # Step 7: Analysis
    print("\n7. Analysis:")
    print(f"Expected behavior:")
    print(f"  - URL identity (root) should take precedence")
    print(f"  - Arguments user_id (previous_user) should be ignored")
    print(f"  - API should be called with user_id=root")
    print(f"Actual result:")
    print(f"  - Effective identity: {effective_identity.get_primary_id()}")
    print(f"  - API params: {api_params}")

if __name__ == "__main__":
    asyncio.run(debug_full_flow()) 