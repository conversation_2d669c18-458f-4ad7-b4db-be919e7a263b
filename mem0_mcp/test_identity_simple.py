#!/usr/bin/env python3
"""
Simple debug script to test identity resolution
"""

import sys
import os

# Add src to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from identity.context_manager import Identity<PERSON>anager, Identity

def test_identity_resolution():
    """Test identity resolution with different scenarios"""
    
    print("=== Testing Identity Resolution ===")
    
    # Test 1: No context
    print("\n1. Testing with no context:")
    try:
        identity = IdentityManager.get_current_identity()
        print(f"Current identity: {identity}")
        print(f"Primary ID: {identity.get_primary_id()}")
        print(f"Is valid: {identity.is_valid()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 2: Set context and test
    print("\n2. Testing with context set:")
    try:
        identity = Identity(user_id="test_user")
        tokens = IdentityManager.set_identity(identity)
        
        current_identity = IdentityManager.get_current_identity()
        print(f"Set identity: {identity}")
        print(f"Current identity: {current_identity}")
        print(f"Primary ID: {current_identity.get_primary_id()}")
        print(f"Is valid: {current_identity.is_valid()}")
        
        IdentityManager.clear_identity(tokens)
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 3: Test with arguments
    print("\n3. Testing with arguments:")
    try:
        arguments = {"user_id": "args_user", "agent_id": "args_agent"}
        effective_identity = IdentityManager.get_effective_identity(arguments)
        print(f"Arguments: {arguments}")
        print(f"Effective identity: {effective_identity}")
        print(f"Primary ID: {effective_identity.get_primary_id()}")
        print(f"Is valid: {effective_identity.is_valid()}")
    except Exception as e:
        print(f"Error: {e}")
    
    # Test 4: Context vs arguments priority
    print("\n4. Testing context vs arguments priority:")
    try:
        # Set context
        context_identity = Identity(user_id="context_user")
        tokens = IdentityManager.set_identity(context_identity)
        
        # Test with arguments
        arguments = {"user_id": "args_user"}
        effective_identity = IdentityManager.get_effective_identity(arguments)
        print(f"Context identity: {context_identity}")
        print(f"Arguments: {arguments}")
        print(f"Effective identity: {effective_identity}")
        print(f"Primary ID: {effective_identity.get_primary_id()}")
        
        IdentityManager.clear_identity(tokens)
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    test_identity_resolution() 