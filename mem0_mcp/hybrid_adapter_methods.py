    
    # Core memory operations with intelligent routing
    async def get_memories(self, **kwargs) -> Dict[str, Any]:
        """Get memories using V2 API with filters structure"""
        try:
            filters = kwargs.get("filters", {})
            for param in ["user_id", "agent_id", "run_id"]:
                if param in kwargs and kwargs[param] is not None:
                    filters[param] = kwargs[param]
            
            payload = {"version": "v2", "filters": filters}
            for param in ["limit"]:
                if param in kwargs and kwargs[param] is not None:
                    payload[param] = kwargs[param]
            
            response = await self.client.get("v2/memories/", params=payload)
            logger.debug(f"HybridAdapter get_memories: {response}")
            return response
        except Exception as e:
            logger.error(f"HybridAdapter get_memories failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get memories: {str(e)}", "get_memories")
    
    async def get_memory_by_id(self, memory_id: str, **kwargs) -> Dict[str, Any]:
        """Get memory by ID using V1 API"""
        try:
            response = await self.client.get(f"memories/{memory_id}")
            logger.debug(f"HybridAdapter get_memory_by_id: {response}")
            return response
        except Exception as e:
            logger.error(f"HybridAdapter get_memory_by_id failed: {str(e)}")
            raise ToolExecutionError(f"Failed to get memory: {str(e)}", "get_memory_by_id")
    
    async def update_memory(self, memory_id: str, data: str, **kwargs) -> Dict[str, Any]:
        """Update memory using V1 API"""
        try:
            payload = {"text": data}
            response = await self.client.put(f"memories/{memory_id}", payload)
            logger.debug(f"HybridAdapter update_memory: {response}")
            return response
        except Exception as e:
            logger.error(f"HybridAdapter update_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to update memory: {str(e)}", "update_memory")
    
    async def delete_memory(self, memory_id: str, **kwargs) -> Dict[str, Any]:
        """Delete memory using V1 API"""
        try:
            response = await self.client.delete(f"memories/{memory_id}")
            logger.debug(f"HybridAdapter delete_memory: {response}")
            return response
        except Exception as e:
            logger.error(f"HybridAdapter delete_memory failed: {str(e)}")
            raise ToolExecutionError(f"Failed to delete memory: {str(e)}", "delete_memory")
    
    # Graph database methods - all use V1 API for graph support
    async def create_graph_entity(self, **kwargs) -> Dict[str, Any]:
        """Create graph entity using V1 API"""
        try:
            response = await self.client.post("entities", kwargs)
            return response
        except Exception as e:
            raise ToolExecutionError(f"Failed to create entity: {str(e)}", "create_graph_entity")
    
    async def get_graph_entities(self, **kwargs) -> Dict[str, Any]:
        """Get graph entities using V1 API"""  
        try:
            response = await self.client.get("entities", params=kwargs)
            return response
        except Exception as e:
            raise ToolExecutionError(f"Failed to get entities: {str(e)}", "get_graph_entities")
    
    async def update_graph_entity(self, entity_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph entity using V1 API"""
        try:
            response = await self.client.put(f"entities/{entity_id}", data)
            return response  
        except Exception as e:
            raise ToolExecutionError(f"Failed to update entity: {str(e)}", "update_graph_entity")
    
    async def delete_graph_entity(self, entity_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph entity using V1 API"""
        try:
            response = await self.client.delete(f"entities/{entity_id}")
            return response
        except Exception as e:
            raise ToolExecutionError(f"Failed to delete entity: {str(e)}", "delete_graph_entity")
    
    async def create_graph_relationship(self, **kwargs) -> Dict[str, Any]:
        """Create graph relationship using V1 API"""
        try:
            response = await self.client.post("relationships", kwargs)
            return response
        except Exception as e:
            raise ToolExecutionError(f"Failed to create relationship: {str(e)}", "create_graph_relationship")
    
    async def get_graph_relationships(self, **kwargs) -> Dict[str, Any]:
        """Get graph relationships using V1 API"""
        try:
            response = await self.client.get("relationships", params=kwargs)
            return response
        except Exception as e:
            raise ToolExecutionError(f"Failed to get relationships: {str(e)}", "get_graph_relationships")
    
    async def update_graph_relationship(self, relationship_id: str, data: Dict[str, Any], **kwargs) -> Dict[str, Any]:
        """Update graph relationship using V1 API"""
        try:
            response = await self.client.put(f"relationships/{relationship_id}", data)
            return response
        except Exception as e:
            raise ToolExecutionError(f"Failed to update relationship: {str(e)}", "update_graph_relationship")
    
    async def delete_graph_relationship(self, relationship_id: str, **kwargs) -> Dict[str, Any]:
        """Delete graph relationship using V1 API"""
        try:
            response = await self.client.delete(f"relationships/{relationship_id}")
            return response
        except Exception as e:
            raise ToolExecutionError(f"Failed to delete relationship: {str(e)}", "delete_graph_relationship")