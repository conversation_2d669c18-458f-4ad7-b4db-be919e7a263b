{"mcpServers": {"mem0-mcp": {"type": "streamable-http", "url": "http://localhost:8001/mcp", "description": "Standard MCP endpoint - requires identity in tool arguments"}, "mem0-mcp-with-user": {"type": "streamable-http", "url": "http://localhost:8001/mcp/user/user123", "description": "Simplified context-aware endpoint with user identity in URL path"}, "mem0-mcp-with-agent": {"type": "streamable-http", "url": "http://localhost:8001/mcp/agent/agent456", "description": "Agent-specific endpoint format"}, "mem0-mcp-with-run": {"type": "streamable-http", "url": "http://localhost:8001/mcp/run/run789", "description": "Run-specific endpoint format"}, "mem0-mcp-default": {"type": "streamable-http", "url": "http://localhost:8001/mcp/user123", "description": "Default identity endpoint (treated as user_id)"}}}