#!/usr/bin/env python3
"""
测试MCP v2功能的完整脚本
"""

import json
import asyncio
import sys
import os

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.protocol.mcp_handler import MCPProtocolHandler
from src.config.settings import MCPConfig


async def test_mcp_v2():
    """测试MCP v2功能"""
    print("🚀 开始测试MCP v2功能...")
    
    try:
        # 初始化配置
        config = MCPConfig()
        config.mem0_api_version = 'v2'
        config.mem0_base_url = 'http://localhost:8000'
        
        # 创建协议处理器
        handler = MCPProtocolHandler(
            server_name="mem0-mcp-server",
            server_version="1.0.0"
        )
        
        print("✅ MCP协议处理器初始化成功")
        
        # 测试1: 添加记忆 (v2)
        print("\n📝 测试1: v2 contextual add...")
        add_request = {
            'jsonrpc': '2.0',
            'id': 1,
            'method': 'tools/call',
            'params': {
                'name': 'add_memory',
                'arguments': {
                    'messages': [
                        {
                            'role': 'user',
                            'content': '通过Python脚本测试MCP v2功能'
                        }
                    ],
                    'user_id': 'python_test_user',
                    'version': 'v2'
                }
            }
        }
        
        add_response = await handler.handle_request(add_request)
        print(f"   Add Response: {json.dumps(add_response, indent=2, ensure_ascii=False)}")
        
        # 测试2: 搜索记忆
        print("\n🔍 测试2: 搜索记忆...")
        search_request = {
            'jsonrpc': '2.0',
            'id': 2,
            'method': 'tools/call',
            'params': {
                'name': 'search_memories',
                'arguments': {
                    'query': 'Python测试',
                    'user_id': 'python_test_user',
                    'limit': 5
                }
            }
        }
        
        search_response = await handler.handle_request(search_request)
        print(f"   Search Response: {json.dumps(search_response, indent=2, ensure_ascii=False)}")
        
        # 测试3: 获取记忆列表
        print("\n📋 测试3: 获取记忆列表...")
        get_request = {
            'jsonrpc': '2.0',
            'id': 3,
            'method': 'tools/call',
            'params': {
                'name': 'get_memories',
                'arguments': {
                    'user_id': 'python_test_user',
                    'limit': 3
                }
            }
        }
        
        get_response = await handler.handle_request(get_request)
        print(f"   Get Response: {json.dumps(get_response, indent=2, ensure_ascii=False)}")
        
        print("\n🎉 所有MCP v2功能测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_mcp_v2())